<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>房产评价</title>
    <script src="https://static.fangxiaoer.com/m/resources/common/js/flexible.js"></script>
    <script src="https://cdn.bootcdn.net/ajax/libs/jquery/2.2.4/jquery.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }

        .container {
            max-width: 750px;
            margin: 0 auto;
            background-color: #fff;
            min-height: 100vh;
        }

        .rating-section {
            padding: 0.4rem 0.32rem;
            background-color: #fff;
        }

        .rating-item {
            display: flex;
            align-items: flex-end;
            justify-content: flex-start;
            padding: 0.32rem 0;
        }

        .rating-item:last-child {
            border-bottom: none;
        }

        .rating-label {
            font-size: 0.37rem;
            color: #333;
            font-weight: 500;
            width: 1.5rem;
            display: inline-block;
        }

        .rating-label.two-chars {
            letter-spacing: 0.32rem;
        }

        .stars {
            display: flex;
            gap: 0.16rem;
        }

        .star {
            width: 0.6rem;
            height: 0.6rem;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .star.empty {
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23ddd"><path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/></svg>') no-repeat center;
            background-size: contain;
        }

        .star.filled {
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23ff6b35"><path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/></svg>') no-repeat center;
            background-size: contain;
        }

        .comment-section {
            margin-top: 0.2rem;
            padding: 0.32rem;
            background-color: #fff;
        }

        .comment-textarea {
            width: 100%;
            min-height: 2.4rem;
            padding: 0.24rem;
            border: 1px solid #e0e0e0;
            border-radius: 0.08rem;
            font-size: 0.37rem;
            line-height: 1.5;
            resize: none;
            outline: none;
            background-color: #fafafa;
        }

        .comment-textarea::placeholder {
            color: #999;
        }

        .char-count {
            text-align: right;
            font-size: 0.37rem;
            color: #999;
            margin-top: 0.16rem;
        }

        .upload-section {
            margin-top: 0.2rem;
            padding: 0.37rem;
            background-color: #fff;
        }

        .upload-container {
            display: flex;
            flex-wrap: wrap;
            gap: 0.16rem;
        }

        .upload-area {
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            width: 1.6rem;
            height: 1.6rem;
            border: 2px dashed #ddd;
            border-radius: 0.08rem;
            cursor: pointer;
            transition: all 0.2s ease;
            background-color: #fafafa;
        }

        .upload-area:hover {
            border-color: #ff6b35;
            background-color: #fff5f2;
        }

        .upload-icon {
            width: 0.48rem;
            height: 0.48rem;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23ccc"><path d="M9 16h6v-6h4l-7-7-7 7h4zm-4 2h14v2H5z"/></svg>') no-repeat center;
            background-size: contain;
        }

        .upload-text {
            font-size: 0.32rem;
            color: #999;
            margin-top: 0.08rem;
            text-align: center;
        }

        .image-preview {
            position: relative;
            width: 1.6rem;
            height: 1.6rem;
            border-radius: 0.08rem;
            overflow: hidden;
            background-color: #f5f5f5;
        }

        .preview-img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .delete-btn {
            position: absolute;
            top: 0.08rem;
            right: 0.08rem;
            width: 0.4rem;
            height: 0.4rem;
            background-color: rgba(0, 0, 0, 0.6);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .delete-btn:hover {
            background-color: rgba(0, 0, 0, 0.8);
        }

        .delete-btn::before {
            content: '×';
            color: #fff;
            font-size: 0.37rem;
            font-weight: bold;
            line-height: 1;
        }

        .upload-limit {
            font-size: 0.32rem;
            color: #999;
            margin-top: 0.16rem;
        }

        .upload-progress {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 0.08rem;
            background-color: rgba(255, 107, 53, 0.2);
            overflow: hidden;
        }

        .progress-bar {
            height: 100%;
            background-color: #ff6b35;
            width: 0%;
            transition: width 0.3s ease;
        }

        .owner-section {
            margin-top: 0.2rem;
            padding: 0.32rem;
            background-color: #fff;
        }

        .owner-info {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .owner-left {
            display: flex;
            align-items: center;
        }

        .owner-checkbox {
            width: 0.32rem;
            height: 0.32rem;
            margin-right: 0.16rem;
            appearance: none;
            -webkit-appearance: none;
            -moz-appearance: none;
            border: 2px solid #999;
            border-radius: 50%;
            background-color: #fff;
            cursor: pointer;
            position: relative;
            transition: all 0.2s ease;
        }

        .owner-checkbox:checked {
            border-color: #E66C00;
            background-color: #fff;
        }

        .owner-checkbox:checked::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 0.12rem;
            height: 0.12rem;
            background-color: #E66C00;
            border-radius: 50%;
        }

        .owner-checkbox:hover {
            border-color: #999;
            background-color: #fff;
        }

        .owner-checkbox:checked:hover {
            border-color: #E66C00;
            background-color: #fff;
        }

        .owner-label {
            font-size: 0.28rem;
            color: #333;
        }

        .owner-tip {
            font-size: 0.32rem;
            color: #ff6b35;
        }

        .disclaimer {
            padding: 0.32rem;
            text-align: center;
            font-size: 0.24rem;
            color: #999;
            background-color: #f8f8f8;
        }

        .submit-btn {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 100%;
            max-width: 750px;
            height: 1rem;
            background-color: #E66C00;
            color: #fff;
            border: none;
            font-size: 0.37rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .submit-btn:hover {
            background-color: #cc5f00;
        }

        .submit-btn:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }

        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 评分区域 -->
        <div class="rating-section">
            <div class="rating-item" data-category="location">
                <span class="rating-label two-chars">地段</span>
                <div class="stars">
                    <div class="star empty" data-rating="1"></div>
                    <div class="star empty" data-rating="2"></div>
                    <div class="star empty" data-rating="3"></div>
                    <div class="star empty" data-rating="4"></div>
                    <div class="star empty" data-rating="5"></div>
                </div>
            </div>

            <div class="rating-item" data-category="traffic">
                <span class="rating-label two-chars">交通</span>
                <div class="stars">
                    <div class="star empty" data-rating="1"></div>
                    <div class="star empty" data-rating="2"></div>
                    <div class="star empty" data-rating="3"></div>
                    <div class="star empty" data-rating="4"></div>
                    <div class="star empty" data-rating="5"></div>
                </div>
            </div>

            <div class="rating-item" data-category="facilities">
                <span class="rating-label two-chars">配套</span>
                <div class="stars">
                    <div class="star empty" data-rating="1"></div>
                    <div class="star empty" data-rating="2"></div>
                    <div class="star empty" data-rating="3"></div>
                    <div class="star empty" data-rating="4"></div>
                    <div class="star empty" data-rating="5"></div>
                </div>
            </div>

            <div class="rating-item" data-category="environment">
                <span class="rating-label two-chars">环境</span>
                <div class="stars">
                    <div class="star empty" data-rating="1"></div>
                    <div class="star empty" data-rating="2"></div>
                    <div class="star empty" data-rating="3"></div>
                    <div class="star empty" data-rating="4"></div>
                    <div class="star empty" data-rating="5"></div>
                </div>
            </div>

            <div class="rating-item" data-category="value">
                <span class="rating-label">性价比</span>
                <div class="stars">
                    <div class="star empty" data-rating="1"></div>
                    <div class="star empty" data-rating="2"></div>
                    <div class="star empty" data-rating="3"></div>
                    <div class="star empty" data-rating="4"></div>
                    <div class="star empty" data-rating="5"></div>
                </div>
            </div>
        </div>

        <!-- 评论区域 -->
        <div class="comment-section">
            <textarea class="comment-textarea" placeholder="说说你对楼盘的印象吧" maxlength="500"></textarea>
            <div class="char-count">还差<span id="remaining-chars">10</span>个字</div>
        </div>

        <!-- 上传图片区域 -->
        <div class="upload-section">
            <div class="upload-container" id="upload-container">
                <div class="upload-area" id="upload-area">
                    <div class="upload-icon"></div>
                    <div class="upload-text">上传照片</div>
                </div>
            </div>
            <div class="upload-limit">最多上传9张图片，每张不超过5MB</div>
            <input type="file" id="file-input" class="hidden" accept="image/jpeg,image/jpg,image/png,image/gif" multiple>
        </div>

        <!-- 业主身份区域 -->
        <div class="owner-section">
            <div class="owner-info">
                <div class="owner-left">
                    <input type="checkbox" id="owner-checkbox" class="owner-checkbox">
                    <label for="owner-checkbox" class="owner-label">我是业主</label>
                </div>
                <div class="owner-tip">业主填写购房信息有神马好处?</div>
            </div>
        </div>

        <!-- 免责声明 -->
        <div class="disclaimer">
            《房小二网用户点评内容管理规范》
        </div>

        <!-- 发布按钮 -->
        <button class="submit-btn" id="submit-btn" >发布</button>
    </div>

    <script>
        $(document).ready(function() {
            // 评分数据
            var ratings = {
                location: 0,
                traffic: 0,
                facilities: 0,
                environment: 0,
                value: 0
            };

            var commentText = '';
            var minChars = 10;
            var uploadedImages = [];
            var maxImages = 9;
            var maxFileSize = 5 * 1024 * 1024; // 5MB

            // 星级评分功能
            $('.star').on('click', function() {
                var $this = $(this);
                var rating = parseInt($this.data('rating'));
                var category = $this.closest('.rating-item').data('category');
                var $stars = $this.closest('.stars').find('.star');

                // 更新评分数据
                ratings[category] = rating;

                // 更新星星显示
                $stars.each(function(index) {
                    var $star = $(this);
                    if (index < rating) {
                        $star.removeClass('empty').addClass('filled');
                    } else {
                        $star.removeClass('filled').addClass('empty');
                    }
                });

                // 检查是否可以提交
                checkSubmitButton();
            });

            // 文本输入功能
            $('.comment-textarea').on('input', function() {
                var text = $(this).val();
                var length = text.length;
                var remaining = Math.max(0, minChars - length);

                commentText = text;

                if (remaining > 0) {
                    $('#remaining-chars').text(remaining);
                    $('.char-count').html('还差<span id="remaining-chars">' + remaining + '</span>个字');
                } else {
                    $('.char-count').html('已输入' + length + '个字');
                }

                // 检查是否可以提交
                checkSubmitButton();
            });

            // 图片上传功能
            $('#upload-area').on('click', function() {
                if (uploadedImages.length < maxImages) {
                    $('#file-input').click();
                } else {
                    alert('最多只能上传' + maxImages + '张图片');
                }
            });

            $('#file-input').on('change', function() {
                var files = this.files;
                if (files.length > 0) {
                    handleFileUpload(files);
                }
                // 清空input，允许重复选择同一文件
                this.value = '';
            });

            // 处理文件上传
            function handleFileUpload(files) {
                for (var i = 0; i < files.length; i++) {
                    if (uploadedImages.length >= maxImages) {
                        alert('最多只能上传' + maxImages + '张图片');
                        break;
                    }

                    var file = files[i];

                    // 检查文件类型
                    if (!file.type.match(/^image\/(jpeg|jpg|png|gif)$/)) {
                        alert('只支持JPG、PNG、GIF格式的图片');
                        continue;
                    }

                    // 检查文件大小
                    if (file.size > maxFileSize) {
                        alert('图片大小不能超过5MB');
                        continue;
                    }

                    // 创建图片预览
                    createImagePreview(file);
                }

                updateUploadArea();
            }

            // 创建图片预览
            function createImagePreview(file) {
                var reader = new FileReader();
                var imageId = 'img_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);

                reader.onload = function(e) {
                    var imageData = {
                        id: imageId,
                        file: file,
                        dataUrl: e.target.result,
                        name: file.name,
                        size: file.size
                    };

                    uploadedImages.push(imageData);

                    // 创建预览元素
                    var previewHtml =
                        '<div class="image-preview" data-id="' + imageId + '">' +
                            '<img class="preview-img" src="' + e.target.result + '" alt="预览图片">' +
                            '<div class="delete-btn" data-id="' + imageId + '"></div>' +
                        '</div>';

                    $('#upload-area').before(previewHtml);
                    updateUploadArea();

                    // 模拟上传进度（实际项目中这里会是真实的上传进度）
                    simulateUploadProgress(imageId);
                };

                reader.readAsDataURL(file);
            }

            // 模拟上传进度
            function simulateUploadProgress(imageId) {
                var $preview = $('.image-preview[data-id="' + imageId + '"]');
                var $progressContainer = $('<div class="upload-progress"><div class="progress-bar"></div></div>');
                $preview.append($progressContainer);

                var progress = 0;
                var interval = setInterval(function() {
                    progress += Math.random() * 30;
                    if (progress >= 100) {
                        progress = 100;
                        clearInterval(interval);
                        setTimeout(function() {
                            $progressContainer.fadeOut(300, function() {
                                $(this).remove();
                            });
                        }, 500);
                    }
                    $progressContainer.find('.progress-bar').css('width', progress + '%');
                }, 200);
            }

            // 删除图片
            $(document).on('click', '.delete-btn', function() {
                var imageId = $(this).data('id');
                var $preview = $('.image-preview[data-id="' + imageId + '"]');

                // 从数组中移除
                uploadedImages = uploadedImages.filter(function(img) {
                    return img.id !== imageId;
                });

                // 移除DOM元素
                $preview.fadeOut(300, function() {
                    $(this).remove();
                    updateUploadArea();
                });
            });

            // 更新上传区域显示
            function updateUploadArea() {
                var $uploadArea = $('#upload-area');
                if (uploadedImages.length >= maxImages) {
                    $uploadArea.hide();
                } else {
                    $uploadArea.show();
                }

                // 更新提示文字
                var remaining = maxImages - uploadedImages.length;
                if (remaining > 0) {
                    $('.upload-limit').text('最多上传' + maxImages + '张图片，还可上传' + remaining + '张，每张不超过5MB');
                } else {
                    $('.upload-limit').text('已达到最大上传数量');
                }
            }

            // 图片压缩函数（可选）
            function compressImage(file, maxWidth, maxHeight, quality, callback) {
                var canvas = document.createElement('canvas');
                var ctx = canvas.getContext('2d');
                var img = new Image();

                img.onload = function() {
                    var ratio = Math.min(maxWidth / img.width, maxHeight / img.height);
                    canvas.width = img.width * ratio;
                    canvas.height = img.height * ratio;

                    ctx.drawImage(img, 0, 0, canvas.width, canvas.height);

                    canvas.toBlob(function(blob) {
                        callback(blob);
                    }, file.type, quality);
                };

                img.src = URL.createObjectURL(file);
            }

            // 检查提交按钮状态
            function checkSubmitButton() {
                var hasRating = Object.values(ratings).some(function(rating) {
                    return rating > 0;
                });
                var hasEnoughText = commentText.length >= minChars;

                if (hasRating && hasEnoughText) {
                    $('#submit-btn').prop('disabled', false);
                } else {
                    $('#submit-btn').prop('disabled', true);
                }
            }

            // 提交功能
            $('#submit-btn').on('click', function() {
                if ($(this).prop('disabled')) {
                    return;
                }

                var isOwner = $('#owner-checkbox').is(':checked');

                var data = {
                    ratings: ratings,
                    comment: commentText,
                    isOwner: isOwner,
                    images: uploadedImages.map(function(img) {
                        return {
                            id: img.id,
                            name: img.name,
                            size: img.size,
                            dataUrl: img.dataUrl
                        };
                    })
                };

                console.log('提交数据:', data);

                // 这里可以发送AJAX请求到服务器
                alert('评价提交成功！');

                // 重置表单
                resetForm();
            });

            // 重置表单
            function resetForm() {
                // 重置评分
                ratings = {
                    location: 0,
                    traffic: 0,
                    facilities: 0,
                    environment: 0,
                    value: 0
                };

                $('.star').removeClass('filled').addClass('empty');

                // 重置文本
                $('.comment-textarea').val('');
                commentText = '';
                $('#remaining-chars').text('10');
                $('.char-count').html('还差<span id="remaining-chars">10</span>个字');

                // 重置复选框
                $('#owner-checkbox').prop('checked', false);

                // 重置图片上传
                uploadedImages = [];
                $('.image-preview').remove();
                $('#file-input').val('');
                updateUploadArea();

                // 禁用提交按钮
                $('#submit-btn').prop('disabled', true);
            }

            // 业主信息提示
            $('.owner-tip').on('click', function() {
                alert('业主填写购房信息可以获得更多曝光机会，帮助您更快出售或出租房屋！');
            });
        });
    </script>
</body>
</html>